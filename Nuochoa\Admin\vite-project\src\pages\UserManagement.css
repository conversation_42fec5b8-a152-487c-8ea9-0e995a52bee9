.user-management {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8f9fa;
  min-height: 100vh;
}

.user-management-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #6f42c1;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Header */
.user-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.header-content h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
  font-weight: 700;
}

.header-content p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.refresh-btn {
  background: linear-gradient(135deg, #6f42c1, #8b5cf6);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(111, 66, 193, 0.3);
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(111, 66, 193, 0.4);
}

/* Stats Grid */
.user-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  border-left: 4px solid;
}

.stat-card.total { border-left-color: #6f42c1; }
.stat-card.new { border-left-color: #28a745; }
.stat-card.active { border-left-color: #17a2b8; }

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0,0,0,0.12);
}

.stat-icon {
  font-size: 48px;
  opacity: 0.9;
}

.stat-content h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.stat-value {
  font-size: 32px;
  font-weight: 800;
  color: #333;
  margin: 0 0 8px 0;
}

.stat-change {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.stat-change.positive {
  color: #28a745;
}

/* Search Section */
.search-section {
  margin-bottom: 24px;
}

.search-box {
  background: white;
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.search-input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.search-input:focus {
  outline: none;
  border-color: #6f42c1;
  background: white;
  box-shadow: 0 0 0 3px rgba(111, 66, 193, 0.1);
}

/* Users Table */
.users-table-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  overflow: hidden;
  margin-bottom: 24px;
}

.users-table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th {
  background: linear-gradient(135deg, #6f42c1, #8b5cf6);
  color: white;
  padding: 20px 16px;
  text-align: left;
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.users-table td {
  padding: 20px 16px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: middle;
}

.users-table tr:hover {
  background-color: #f8f9ff;
}

/* User Info */
.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6f42c1, #8b5cf6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(111, 66, 193, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.user-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.user-status {
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 12px;
  font-weight: 600;
}

.user-status.active {
  background: #d4edda;
  color: #155724;
}

.customer-level {
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.user-email {
  color: #666;
  font-size: 14px;
}

.orders-badge {
  background: #e3f2fd;
  color: #1976d2;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 600;
}

.user-spent {
  font-weight: 600;
  color: #2e7d32;
}

.last-order, .join-date {
  color: #666;
  font-size: 14px;
}

.no-orders {
  color: #999;
  font-style: italic;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-view {
  background: #6f42c1;
  color: white;
  border: none;
  padding: 10px 12px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.btn-view:hover {
  background: #5a2d91;
  transform: translateY(-1px);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 24px;
  background: white;
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.pagination-btn {
  background: #6f42c1;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #5a2d91;
  transform: translateY(-1px);
}

.pagination-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.pagination-info {
  font-weight: 600;
  color: #333;
}

/* No Users */
.no-users {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.no-users-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal {
  background: white;
  border-radius: 20px;
  width: 90%;
  max-width: 700px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #f8f9ff, #e3f2fd);
  border-radius: 20px 20px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 700;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(0,0,0,0.1);
  color: #333;
}

.modal-content {
  padding: 24px;
}

.user-detail-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #f8f9ff, #e3f2fd);
  border-radius: 16px;
}

.user-detail-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6f42c1, #8b5cf6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 32px;
  box-shadow: 0 8px 24px rgba(111, 66, 193, 0.3);
}

.user-detail-info h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
  font-weight: 700;
}

.user-detail-info p {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 16px;
}

.user-badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
}

.user-badge.active {
  background: #d4edda;
  color: #155724;
}

.user-stats-detail {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-item {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  border: 1px solid #e9ecef;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.stat-item .stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.recent-orders-section h4 {
  margin-bottom: 20px;
  color: #333;
  font-size: 18px;
  font-weight: 700;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-item {
  display: grid;
  grid-template-columns: auto 1fr auto auto;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  align-items: center;
  font-size: 14px;
  border: 1px solid #e9ecef;
}

.order-id {
  font-family: monospace;
  font-weight: bold;
  color: #6f42c1;
}

.order-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: capitalize;
}

.status-pending { background: #fff3cd; color: #856404; }
.status-processing { background: #d1ecf1; color: #0c5460; }
.status-shipped { background: #d4edda; color: #155724; }
.status-delivered { background: #d1ecf1; color: #0c5460; }

/* Responsive */
@media (max-width: 768px) {
  .user-management {
    padding: 16px;
  }
  
  .user-management-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .user-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .users-table-container {
    overflow-x: auto;
  }
  
  .users-table {
    min-width: 800px;
  }
  
  .user-stats-detail {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .order-item {
    grid-template-columns: 1fr;
    text-align: center;
  }
}
