.comment-management {
  padding: 20px;
}

.comment-management h2 {
  text-align: center;
}

.review-list {
  margin-top: 20px;
}

.review-item {
  border: 1px solid #e0e0e0;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 12px;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.2s ease;
}

.review-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.review-user {
  font-weight: bold;
  flex: 1;
}

.review-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.review-actions button {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 13px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
}

.review-actions button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.edit-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
}

.delete-btn {
  background-color: red;
  color: white;
  border: none;
}

.report-btn {
  background-color: orange;
  color: white;
  border: none;
}

.review-date {
  font-size: 12px;
  color: #666;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  width: 400px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}

.modal h3 {
  margin-bottom: 15px;
}

.modal textarea {
  width: 100%;
  height: 100px;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #ccc;
  margin-bottom: 20px;
}

.modal-actions {
  display: flex;
  justify-content: space-between;
}

.save-btn, .cancel-btn {
  padding: 8px 15px;
  font-size: 14px;
  cursor: pointer;
}

.save-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
}

.cancel-btn {
  background-color: #f44336;
  color: white;
  border: none;
}

/* Product info styles */
.product-info {
  margin: 10px 0;
}

.product-details {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-top: 8px;
  padding: 10px;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.product-image {
  flex-shrink: 0;
}

.product-thumbnail {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
  border: 1px solid #ddd;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.product-thumbnail:hover {
  transform: scale(1.05);
  cursor: pointer;
}

.product-name {
  font-weight: 500;
  color: #333;
  font-size: 14px;
  line-height: 1.4;
}

.no-product {
  color: #999;
  font-style: italic;
}

/* Rating stars */
.review-user p {
  margin: 5px 0;
}

.review-user p:first-of-type {
  font-size: 18px;
  margin: 8px 0;
}

/* Comment content */
.review-item > p:not(.review-date) {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #007bff;
  margin: 15px 0;
  font-style: italic;
  line-height: 1.5;
}

/* Order info */
.review-user p:last-child {
  color: #666;
  font-size: 13px;
  font-weight: normal;
}

/* Responsive design */
@media (max-width: 768px) {
  .review-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .review-actions {
    justify-content: flex-end;
  }

  .product-details {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 10px;
  }

  .product-thumbnail {
    width: 120px;
    height: 120px;
  }

  .review-actions button {
    padding: 6px 12px;
    font-size: 12px;
  }
}

.save-btn:hover, .cancel-btn:hover {
  opacity: 0.8;
}
