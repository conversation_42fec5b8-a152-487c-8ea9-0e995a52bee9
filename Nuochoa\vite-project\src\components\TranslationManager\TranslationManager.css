/* TranslationManager.css */
.translation-manager {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.translation-manager h2 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.translation-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.product-list h3 {
  margin-bottom: 15px;
  color: #555;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 15px;
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid #ddd;
  padding: 15px;
  border-radius: 8px;
}

.product-card {
  border: 2px solid #eee;
  border-radius: 8px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.product-card:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0,123,255,0.2);
}

.product-card.selected {
  border-color: #007bff;
  background: #f8f9ff;
  box-shadow: 0 2px 12px rgba(0,123,255,0.3);
}

.product-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 8px;
}

.product-info h4 {
  font-size: 14px;
  margin: 0 0 5px 0;
  color: #333;
  line-height: 1.3;
}

.product-info p {
  font-size: 12px;
  color: #666;
  margin: 0;
}

.translation-form {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  background: #f9f9f9;
}

.translation-form h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #555;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.2);
}

.form-group .readonly {
  background: #f5f5f5;
  color: #666;
  cursor: not-allowed;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.save-btn,
.export-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-btn {
  background: #28a745;
  color: white;
}

.save-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

.export-btn {
  background: #007bff;
  color: white;
}

.export-btn:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

/* Responsive */
/* Desktop lớn - 5 sản phẩm */
@media (min-width: 1200px) {
  .product-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* Desktop vừa - 4 sản phẩm */
@media (max-width: 1199px) and (min-width: 992px) {
  .product-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Tablet - 3 sản phẩm */
@media (max-width: 991px) and (min-width: 769px) {
  .product-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Mobile lớn - 2 sản phẩm */
@media (max-width: 768px) and (min-width: 481px) {
  .translation-content {
    grid-template-columns: 1fr;
  }

  .product-grid {
    grid-template-columns: repeat(2, 1fr);
    max-height: 400px;
  }
}

/* Mobile nhỏ - 1 sản phẩm */
@media (max-width: 480px) {
  .translation-content {
    grid-template-columns: 1fr;
  }

  .product-grid {
    grid-template-columns: 1fr;
    max-height: 400px;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
