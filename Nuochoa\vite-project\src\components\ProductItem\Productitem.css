/* ===== Layout chung ===== */
.product-list {
  display: grid;
  grid-template-columns: repeat(5, 1fr); /* 5 cột */
  gap: 20px;
  justify-items: center;
  max-width: 1300px; /* T<PERSON>ng n<PERSON>u cần */
  margin: auto;
  padding: 20px;
  align-items: stretch; 
  
}


.product-item {
  background: #fff;
  border-radius: 15px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 250px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: box-shadow 0.2s ease;
   justify-content: space-between;
}

.product-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* ===== Ảnh sản phẩm ===== */
.food-item-img-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  overflow: hidden;
}

.Product-item-image {
  max-height: 180px;
  width: auto;
  object-fit: contain;
}

/* ===== Nội dung ===== */
.product-item-info {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex-grow: 1;
  padding-top: 10px;
  
}

.product-item-brand {
  font-weight: 600;
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  margin-bottom: 4px;
  letter-spacing: 0.5px;
}

.product-item-name {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 6px;
  line-height: 1.4;
  color: #2c3e50;
}

.product-item-desc {
  font-size: 13px;
  color: #666;
  flex-grow: 1;
  margin-bottom: 10px;
  line-height: 1.5;
}

/* ===== Dung tích (size) ===== */
.product-item-sizes {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.product-size-btn {
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #ddd;
  border-radius: 20px;
  background: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  letter-spacing: 0.2px;
}

.product-size-btn:hover {
  background-color: #f3f3f3;
  border-color: #bbb;
}

.product-size-btn.active {
  background-color: #000;
  color: #fff;
  border-color: #000;
}

/* ===== Giá & giảm giá ===== */
.product-item-price {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 6px;
}

.new-price {
  color: #e60023;
  font-size: 18px;
  font-weight: 700;
}

.old-price {
  color: #999;
  font-size: 14px;
  text-decoration: line-through;
}

.discount {
  background-color: #e6f4ea;
  color: #00a651;
  font-size: 13px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 6px;
}

/* ===== SALE badge ===== */
.discount-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background-color: #e60023;
  color: #fff;
  padding: 4px 10px;
  border-radius: 4px;
  font-weight: 700;
  font-size: 13px;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0,0,0,0.15);
}

/* ===== Action buttons ===== */
.product-item-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 10;
}

.add-to-cart-btn {
  background-color: #f55a2c !important;
  border: 1px solid #f55a2c !important;
  color: white !important;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer !important;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  position: relative;
  z-index: 10;
  pointer-events: auto !important;
  min-width: 80px;
  letter-spacing: 0.3px;
}

.add-to-cart-btn.compact {
  padding: 8px;
  min-width: 40px;
  font-size: 16px;
  border-radius: 50%;
  width: 40px;
  height: 40px;
}

.add-to-cart-btn:hover {
  background-color: #e04a1c !important;
  border-color: #e04a1c !important;
}

.add-to-cart-btn:active {
  background-color: #d03f17 !important;
}

.wishlist-btn-item {
  flex-shrink: 0;
}

.product-price {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 10px;
}

.original-price {
  text-decoration: line-through;
  color: #999;
  font-size: 14px;
}

.sale-price {
  color: #e60023;
  font-size: 18px;
  font-weight: 600;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
  .product-item-actions {
    gap: 6px;
    margin-top: 10px;
  }

  .add-to-cart-btn {
    padding: 6px 8px;
    font-size: 12px;
  }

  .product-size-btn {
    padding: 4px 8px;
    font-size: 11px;
  }
}




  .wishlist-btn-item {
    align-self: center;
  }

/* ===== HẾT HÀNG ===== */
.out-of-stock-badge {
  position: absolute;
  top: 25%; 
  left: 50%;
  transform: translate(-50%, -50%); /* Căn giữa thông báo */
  background-color: rgba(255, 255, 255, 0.7); /* Nền trắng với độ mờ */
  color: black; /* Chữ đen */
  font-size: 12px; /* Kích thước chữ */
  font-weight: 600; /* Đậm chữ */
  text-transform: uppercase;
  padding: 10px 20px; /* Thêm khoảng đệm cho chữ */
  border-radius: 8px; /* Bo góc */
  z-index: 10; /* Đảm bảo thông báo ở trên sản phẩm */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Tạo bóng nhẹ */
  width: 80%; /* Đảm bảo chữ "Hết hàng" không bị dài quá */
  text-align: center;
  font-family: 'Arial', sans-serif;
  letter-spacing: 1px;
}

/* Khi sản phẩm hết hàng, làm nền của item mờ */


/* Sửa lại phần ảnh sản phẩm */
.food-item-img-container {
  position: relative; /* Để "Hết hàng" hiển thị trên ảnh sản phẩm */
  display: flex;
  justify-content: center;
  align-items: center;
}
