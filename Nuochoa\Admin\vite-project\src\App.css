/* Modern Admin App Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #f8f9fa;
  color: #2d3748;
  line-height: 1.6;
}

.app {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  overflow: hidden;
}

/* Main Content Area */
.main-content {
  flex: 1;
  overflow-y: auto;
  background: transparent;
  position: relative;
}

/* Scrollbar Styling */
.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: transparent;
}

.main-content::-webkit-scrollbar-thumb {
  background: rgba(245, 90, 44, 0.3);
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: rgba(245, 90, 44, 0.5);
}

/* Global Button Styles */
.btn {
  padding: 12px 24px;
  border-radius: 12px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: linear-gradient(135deg, #f55a2c, #ff8c42);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 90, 44, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 90, 44, 0.4);
}

.btn-secondary {
  background: white;
  color: #f55a2c;
  border: 2px solid #f55a2c;
}

.btn-secondary:hover {
  background: #f55a2c;
  color: white;
  transform: translateY(-2px);
}

/* Card Styles */
.card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  border: 1px solid rgba(255,255,255,0.2);
  overflow: hidden;
}

.card-header {
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid #e2e8f0;
}

.card-body {
  padding: 24px;
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  color: #374151;
  font-weight: 500;
  font-size: 14px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #f55a2c;
  box-shadow: 0 0 0 3px rgba(245, 90, 44, 0.1);
}

/* Table Styles */
.table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.table th,
.table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.table tr:hover {
  background: #f8f9fa;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mt-4 { margin-top: 1rem; }
.p-4 { padding: 1rem; }
.rounded { border-radius: 12px; }
.shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }

/* Responsive */
@media (max-width: 768px) {
  .app {
    flex-direction: column;
  }

  .main-content {
    padding: 16px;
  }
}
