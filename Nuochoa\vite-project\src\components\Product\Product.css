
.product h2{
    font-size: max(2vw,26px);
    font-weight: 600;
    text-align: center;
    letter-spacing: 0.3px;
    color: #2c3e50;
    line-height: 1.3;
}
.product-list {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
  justify-items: center;
  max-width: 1400px;
  margin: auto;
}

.product {
  max-width: 1400px;
  margin: auto;
  padding: 0 20px;
}

.product h2 {
  font-size: 26px;
  margin-top: 40px;
  margin-bottom: 20px;
  font-weight: 600;
  letter-spacing: 0.3px;
  color: #2c3e50;
  line-height: 1.3;
}

.checkbox-filter {
  display: flex;
  gap: 20px;
  margin: 20px 0;
  font-size: 16px;
}

.checkbox-filter label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.checkbox-filter {
  display: flex;
  gap: 25px;
  margin: 25px 0;
  font-size: 15px;
  font-weight: 500;
  flex-wrap: wrap;
  letter-spacing: 0.2px;
}

.custom-checkbox {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  user-select: none;
  color: #333;
  font-weight: 500;
}

.custom-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.custom-checkbox .checkmark {
  position: absolute;
  left: 0;
  top: 1px;
  height: 18px;
  width: 18px;
  background-color: #eee;
  border-radius: 4px;
  border: 1px solid #ccc;
  transition: 0.2s;
}

.custom-checkbox:hover .checkmark {
  background-color: #ddd;
}

.custom-checkbox input:checked ~ .checkmark {
  background-color: #007bff;
  border-color: #007bff;
}

.custom-checkbox .checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.custom-checkbox input:checked ~ .checkmark:after {
  display: block;
}

.custom-checkbox .checkmark:after {
  left: 5px;
  top: 1px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.filter-bar {
  display: flex;
  gap: 10px;
  margin: 20px 0;
  flex-wrap: wrap;
}

.filter-bar select,
.filter-bar button {
  padding: 10px 14px;
  border: 1px solid #ccc;
  background-color: #f4f6fc;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  letter-spacing: 0.2px;
}

.filter-bar button.active {
  background-color: #d0e2ff;
  font-weight: bold;
  border-color: #4682f0;
}

.reset-filters-btn {
  background-color: #ff6b6b !important;
  color: white !important;
  border-color: #ff5252 !important;
}

.reset-filters-btn:hover {
  background-color: #ff5252 !important;
  transform: translateY(-1px);
}

.products-count {
  margin: 10px 0 20px 0;
  text-align: center;
}

.products-count p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.filter-info {
  color: #4682f0;
  font-weight: bold;
}



/* ===== PRODUCT SECTIONS ===== */
.product-section {
  margin: 40px 0;
}

.product-section:not(:last-child) {
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 40px;
}

.section-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.section-header.simple {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Header khuyến mãi siêu gọn */
.product-section:first-child .section-header.simple {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.25);
  position: relative;
  padding: 8px 16px;
  border-radius: 6px;
  margin-bottom: 15px;
}

.product-section:first-child .section-header.simple::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.15) 50%, transparent 60%);
  animation: goldShine 4s ease-in-out infinite;
  border-radius: 6px;
}

@keyframes goldShine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 2px 0;
  position: relative;
  z-index: 1;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1.2;
}

.section-subtitle {
  font-size: 11px;
  margin: 0;
  opacity: 0.85;
  position: relative;
  z-index: 1;
  font-weight: 400;
  line-height: 1.3;
}

/* Title vàng đơn giản */
.gold-title {
  color: #FFD700;
  font-weight: 700;
}

/* Hiệu ứng đặc biệt cho section khuyến mãi */
.promotional-section {
  position: relative;
  transition: all 0.3s ease;
  border-radius: 6px;
}

.promotional-section:hover::before {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.05), rgba(255, 165, 0, 0.05));
  border-color: rgba(255, 215, 0, 0.15);
}

/* Background nhẹ để nổi bật vừa phải */
.promotional-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.03), rgba(255, 165, 0, 0.03));
  border-radius: 6px;
  z-index: -1;
  border: 1px solid rgba(255, 215, 0, 0.1);
  box-shadow: 0 1px 3px rgba(255, 215, 0, 0.1);
}

.promotional-section .product-list {
  position: relative;
  z-index: 1;
}

/* Hiệu ứng siêu gọn cho sản phẩm trong section khuyến mãi */
.promotional-section .product-item {
  transition: all 0.25s ease;
  position: relative;
}

.promotional-section .product-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 215, 0, 0.06), rgba(255, 165, 0, 0.06));
  border-radius: 8px;
  opacity: 0;
  transition: all 0.25s ease;
  z-index: -1;
}

.promotional-section .product-item:hover::before {
  opacity: 1;
}

.promotional-section .product-item:hover {
  transform: translateY(-4px) scale(1.01);
  box-shadow: 0 6px 15px rgba(255, 215, 0, 0.15);
}

/* Removed goldBadgePulse animation for cleaner look */

/* ===== GENDER SECTIONS STYLING ===== */
.regular-section .section-header.simple {
  background: none !important;
  border: none !important;
  padding: 0 !important;
  margin-bottom: 20px !important;
  text-align: left !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

.regular-section .section-title {
  color: #333 !important;
  font-size: 24px !important;
  font-weight: 600 !important;
  margin: 0 0 5px 0 !important;
  text-transform: none !important;
  letter-spacing: normal !important;
}

.regular-section .section-subtitle {
  display: none !important;
}

/* ===== NO PRODUCTS MESSAGE ===== */
.no-products {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 18px;
  background: #f8f9fa;
  border-radius: 12px;
  margin: 40px 0;
}

/* ===== RESPONSIVE DESIGN ===== */
/* Desktop lớn - 5 sản phẩm */
@media (min-width: 1200px) {
  .product-list {
    grid-template-columns: repeat(5, 1fr);
    max-width: 1400px;
  }

  .product {
    max-width: 1400px;
  }
}

/* Desktop vừa - 4 sản phẩm */
@media (max-width: 1199px) and (min-width: 992px) {
  .product-list {
    grid-template-columns: repeat(4, 1fr);
    max-width: 1200px;
  }

  .product {
    max-width: 1200px;
  }
}

/* Tablet - 3 sản phẩm */
@media (max-width: 991px) and (min-width: 769px) {
  .product-list {
    grid-template-columns: repeat(3, 1fr);
    max-width: 900px;
  }

  .product {
    max-width: 900px;
  }
}

/* Mobile lớn - 2 sản phẩm */
@media (max-width: 768px) and (min-width: 481px) {
  .product-list {
    grid-template-columns: repeat(2, 1fr);
    max-width: 600px;
    gap: 15px;
  }

  .product {
    max-width: 600px;
  }

  .section-title {
    font-size: 14px;
  }

  .section-subtitle {
    font-size: 10px;
  }

  .section-header {
    padding: 6px 12px;
    margin-bottom: 15px;
  }

  .product-section {
    margin: 20px 0;
  }

  .product-section:not(:last-child) {
    padding-bottom: 20px;
  }

  .promotional-section:hover::before {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.04), rgba(255, 165, 0, 0.04));
    border-color: rgba(255, 215, 0, 0.12);
  }

  .promotional-section .product-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 10px rgba(255, 215, 0, 0.12);
  }

  .product-section:first-child .section-header.simple {
    padding: 6px 12px;
  }

  /* Regular sections responsive */
  .regular-section .section-title {
    font-size: 20px !important;
  }

  .regular-section .section-header.simple {
    margin-bottom: 15px !important;
    padding: 0 !important;
    text-align: left !important;
  }
}

/* Mobile nhỏ - 1 sản phẩm */
@media (max-width: 480px) {
  .product-list {
    grid-template-columns: 1fr;
    max-width: 350px;
    gap: 15px;
  }

  .product {
    max-width: 350px;
  }

  .section-title {
    font-size: 13px;
  }

  .section-subtitle {
    font-size: 9px;
  }

  .section-header {
    padding: 5px 10px;
  }

  .promotional-section .product-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(255, 215, 0, 0.1);
  }

  .product-section:first-child .section-header.simple {
    padding: 5px 10px;
    margin-bottom: 12px;
  }

  /* Regular sections mobile */
  .regular-section .section-title {
    font-size: 18px !important;
  }

  .regular-section .section-header.simple {
    margin-bottom: 12px !important;
    padding: 0 !important;
    text-align: left !important;
  }
}

