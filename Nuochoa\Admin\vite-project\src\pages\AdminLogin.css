.admin-login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-container {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  max-width: 500px;
  width: 100%;
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h1 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 32px;
  font-weight: bold;
}

.login-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.admin-login-form {
  text-align: center;
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
  text-align: left;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 600;
  font-size: 14px;
}

.form-group input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s, box-shadow 0.3s;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #f55a2c;
  box-shadow: 0 0 0 3px rgba(245, 90, 44, 0.1);
}

.form-group input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #fcc;
  font-size: 14px;
}

.admin-login-btn {
  width: 100%;
  background: linear-gradient(135deg, #f55a2c 0%, #ff8c42 100%);
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(245, 90, 44, 0.3);
}

.admin-login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 90, 44, 0.4);
}

.admin-login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.admin-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #f55a2c;
  text-align: left;
}

.admin-info h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.admin-info ul {
  margin: 0;
  padding-left: 20px;
  color: #666;
}

.admin-info li {
  margin-bottom: 8px;
  font-size: 14px;
}

/* Responsive */
@media (max-width: 768px) {
  .admin-login {
    padding: 10px;
  }
  
  .login-container {
    padding: 30px 20px;
  }
  
  .login-header h1 {
    font-size: 28px;
  }
  
  .form-group input {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}
