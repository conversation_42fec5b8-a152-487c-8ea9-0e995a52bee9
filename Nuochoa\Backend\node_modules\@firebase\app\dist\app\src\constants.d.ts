/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * The default app name
 *
 * @internal
 */
export declare const DEFAULT_ENTRY_NAME = "[DEFAULT]";
export declare const PLATFORM_LOG_STRING: {
    readonly [x: string]: "fire-core" | "fire-core-compat" | "fire-analytics" | "fire-analytics-compat" | "fire-app-check" | "fire-app-check-compat" | "fire-auth" | "fire-auth-compat" | "fire-rtdb" | "fire-data-connect" | "fire-rtdb-compat" | "fire-fn" | "fire-fn-compat" | "fire-iid" | "fire-iid-compat" | "fire-fcm" | "fire-fcm-compat" | "fire-perf" | "fire-perf-compat" | "fire-rc" | "fire-rc-compat" | "fire-gcs" | "fire-gcs-compat" | "fire-fst" | "fire-fst-compat" | "fire-vertex" | "fire-js" | "fire-js-all";
    readonly 'fire-js': "fire-js";
};
