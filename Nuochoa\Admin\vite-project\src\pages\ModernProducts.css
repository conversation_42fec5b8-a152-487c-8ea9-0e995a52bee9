/* Modern Products Page */
.modern-products {
  padding: 24px;
  background: transparent;
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Loading State */
.products-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: #666;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #f55a2c;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Header Section */
.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 32px;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  margin-bottom: 24px;
  border: 1px solid rgba(255,255,255,0.2);
}

.header-content h1 {
  font-size: 32px;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #f55a2c, #ff8c42);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-content p {
  color: #718096;
  font-size: 16px;
  margin: 0;
}

.add-product-btn {
  background: linear-gradient(135deg, #f55a2c, #ff8c42);
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  box-shadow: 0 4px 15px rgba(245, 90, 44, 0.3);
}

.add-product-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 90, 44, 0.4);
}

/* Controls Section */
.products-controls {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 20px;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  margin-bottom: 24px;
}

.search-section {
  display: flex;
  gap: 16px;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 16px;
}

.search-box input {
  width: 100%;
  padding: 12px 16px 12px 48px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  background: #f9fafb;
}

.search-box input:focus {
  outline: none;
  border-color: #f55a2c;
  background: white;
  box-shadow: 0 0 0 3px rgba(245, 90, 44, 0.1);
}

.filter-section {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
  min-width: 150px;
}

.filter-select:focus {
  outline: none;
  border-color: #f55a2c;
}

.view-controls {
  display: flex;
  gap: 8px;
  background: #f3f4f6;
  padding: 4px;
  border-radius: 12px;
}

.view-btn {
  padding: 12px;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #6b7280;
}

.view-btn.active {
  background: white;
  color: #f55a2c;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Stats Section */
.products-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-item {
  background: white;
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  text-align: center;
  border: 1px solid rgba(255,255,255,0.2);
}

.stat-number {
  display: block;
  font-size: 28px;
  font-weight: 700;
  color: #f55a2c;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

/* Products Container */
.products-container {
  display: grid;
  gap: 16px;
}

.products-container.grid {
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
}

.products-container.list {
  grid-template-columns: 1fr;
}

/* No Products State */
.no-products {
  grid-column: 1 / -1;
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.no-products-icon {
  font-size: 80px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.no-products h3 {
  font-size: 24px;
  color: #2d3748;
  margin-bottom: 12px;
}

.no-products p {
  color: #718096;
  margin-bottom: 24px;
}

.add-first-product-btn {
  background: linear-gradient(135deg, #f55a2c, #ff8c42);
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 15px rgba(245, 90, 44, 0.3);
}

.add-first-product-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 90, 44, 0.4);
}

/* Product Card */
.product-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(255,255,255,0.2);
  position: relative;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.product-image {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.action-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.3s ease;
}

.action-btn.view {
  background: #17a2b8;
  color: white;
}

.action-btn.edit {
  background: #28a745;
  color: white;
}

.action-btn.delete {
  background: #dc3545;
  color: white;
}

.action-btn:hover {
  transform: scale(1.1);
}

.product-info {
  padding: 12px 16px;
}

.product-header {
  margin-bottom: 8px;
}

.product-name {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 2px 0;
  line-height: 1.2;
}

.product-brand {
  font-size: 12px;
  color: #718096;
  font-weight: 500;
}

.product-category {
  margin-bottom: 16px;
}

.category-tag {
  background: linear-gradient(135deg, rgba(245, 90, 44, 0.1), rgba(255, 140, 66, 0.1));
  color: #f55a2c;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid rgba(245, 90, 44, 0.2);
}

.product-prices {
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.original-price {
  text-decoration: line-through;
  color: #9ca3af;
  font-size: 11px;
}

.current-price {
  font-size: 14px;
  font-weight: 700;
  color: #f55a2c;
}

.import-price {
  font-size: 10px;
  color: #6b7280;
}

.product-sizes {
  margin-bottom: 10px;
}

.sizes-label {
  font-size: 10px;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 4px;
  display: block;
}

.sizes-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.size-tag {
  background: #f3f4f6;
  color: #374151;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 9px;
  font-weight: 500;
}

.product-description {
  margin-bottom: 12px;
}

.product-description p {
  color: #6b7280;
  font-size: 11px;
  line-height: 1.3;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-actions {
  display: flex;
  gap: 8px;
}

.btn-edit,
.btn-delete {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 11px;
}

.btn-edit {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-edit:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-delete {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-delete:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

/* List View Styles */
.products-container.list .product-card {
  display: grid;
  grid-template-columns: 100px 1fr;
  gap: 16px;
  align-items: center;
}

.products-container.list .product-image {
  height: 80px;
}

.products-container.list .product-overlay {
  display: none;
}

.products-container.list .product-actions {
  margin-top: 16px;
}

/* Responsive */
@media (max-width: 1200px) {
  .products-controls {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .filter-section {
    justify-content: center;
  }

  .view-controls {
    justify-self: center;
  }
}

@media (max-width: 768px) {
  .modern-products {
    padding: 16px;
  }

  .products-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
    padding: 24px;
  }

  .header-content h1 {
    font-size: 24px;
  }

  .products-controls {
    padding: 20px;
  }

  .search-box {
    max-width: none;
  }

  .filter-section {
    flex-direction: column;
    gap: 12px;
  }

  .filter-select {
    min-width: auto;
  }

  .products-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .products-container.grid {
    grid-template-columns: 1fr;
  }

  .products-container.list .product-card {
    grid-template-columns: 1fr;
  }

  .products-container.list .product-image {
    height: 100px;
  }

  .product-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .products-stats {
    grid-template-columns: 1fr;
  }

  .stat-item {
    padding: 16px;
  }

  .stat-number {
    font-size: 24px;
  }

  .product-card {
    margin-bottom: 16px;
  }

  .product-info {
    padding: 10px 12px;
  }
}

/* Inventory Section Styles */
.product-inventory {
  margin-bottom: 12px;
  padding: 8px;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.inventory-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.inventory-label {
  font-size: 11px;
  font-weight: 600;
  color: #475569;
}

.inventory-count {
  font-size: 13px;
  font-weight: 700;
  padding: 2px 8px;
  border-radius: 12px;
  min-width: 30px;
  text-align: center;
}

.inventory-count.in-stock {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.inventory-count.low-stock {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.inventory-count.out-of-stock {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.inventory-actions {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.inventory-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 28px;
}

.inventory-btn.decrease {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.inventory-btn.decrease:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.inventory-btn.decrease:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
}

.inventory-btn.increase {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.inventory-btn.increase:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

/* Responsive for inventory */
@media (max-width: 768px) {
  .inventory-actions {
    gap: 6px;
  }

  .inventory-btn {
    padding: 6px 10px;
    font-size: 11px;
    min-width: 32px;
  }
}
