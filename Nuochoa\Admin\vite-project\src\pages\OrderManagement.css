.order-management {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.page-header h1 {
  margin: 0;
  color: #333;
  font-size: 28px;
}

.refresh-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.refresh-btn:hover {
  background: #0056b3;
}

/* Loading */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Order Statistics */
.order-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: box-shadow 0.2s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.stat-card h3 {
  margin: 0 0 10px 0;
  font-size: 32px;
  font-weight: bold;
}

.stat-card p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.stat-card.total h3 { color: #333; }
.stat-card.pending h3 { color: #ffc107; }
.stat-card.paid h3 { color: #28a745; }
.stat-card.processing h3 { color: #17a2b8; }
.stat-card.shipped h3 { color: #6f42c1; }
.stat-card.delivered h3 { color: #20c997; }
.stat-card.cancelled h3 { color: #dc3545; }

/* Filters */
.filters-section {
  background: white;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  display: flex;
  gap: 30px;
  align-items: end;
  flex-wrap: wrap;
}

.filter-group, .search-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group label, .search-group label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.filter-select, .search-input {
  padding: 10px;
  border: 2px solid #e9ecef;
  border-radius: 5px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.filter-select:focus, .search-input:focus {
  outline: none;
  border-color: #007bff;
}

.search-input {
  min-width: 300px;
}

/* Orders Table */
.orders-table-container {
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.orders-table {
  width: 100%;
  border-collapse: collapse;
}

.orders-table th {
  background: #f8f9fa;
  padding: 15px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #dee2e6;
}

.orders-table td {
  padding: 15px;
  border-bottom: 1px solid #dee2e6;
  vertical-align: middle;
}

.orders-table tr:hover {
  background-color: #f8f9fa;
}

.order-id {
  font-family: monospace;
  background: #e9ecef;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.customer-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.customer-name {
  font-weight: 600;
  color: #333;
}

.customer-email {
  font-size: 12px;
  color: #666;
}

.order-amount {
  font-weight: 600;
  color: #28a745;
}

.status-badge {
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.btn-edit {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.3s;
}

.btn-edit:hover {
  background: #0056b3;
}

.no-orders {
  text-align: center;
  padding: 40px;
  color: #666;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  padding: 30px;
  border-radius: 10px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 24px;
}

.order-details {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.detail-row {
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #dee2e6;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row strong {
  display: inline-block;
  width: 120px;
  color: #333;
}

.status-update {
  margin-bottom: 30px;
}

.status-update label {
  display: block;
  margin-bottom: 15px;
  font-weight: 600;
  color: #333;
}

.status-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.status-btn {
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  transition: opacity 0.3s;
}

.status-btn:hover:not(:disabled) {
  opacity: 0.8;
}

.status-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.status-btn.active {
  box-shadow: 0 0 0 3px rgba(255,255,255,0.5);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.btn-cancel {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-cancel:hover:not(:disabled) {
  background: #545b62;
}

.btn-cancel:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive */
@media (max-width: 768px) {
  .order-management {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .order-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .filters-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input {
    min-width: auto;
  }
  
  .orders-table-container {
    overflow-x: auto;
  }
  
  .orders-table {
    min-width: 800px;
  }
  
  .modal {
    width: 95%;
    padding: 20px;
  }
  
  .status-buttons {
    justify-content: center;
  }
}
